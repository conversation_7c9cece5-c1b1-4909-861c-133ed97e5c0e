"use client";

import * as React from "react";
import { useLocale } from "next-intl";
import { useRouter, usePathname } from "next/navigation";
import { ChevronsUpDown, CheckIcon } from "lucide-react";

import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { cn } from "@/lib/utils";

// 语言配置
const languages = [
  {
    code: "en",
    name: "English",
    flag: "🇬🇧",
  },
  {
    code: "zh",
    name: "中文",
    flag: "🇨🇳",
  },
  {
    code: "ja",
    name: "日本語",
    flag: "🇯🇵",
  },
  {
    code: "de",
    name: "<PERSON><PERSON><PERSON>",
    flag: "🇩🇪",
  },
  {
    code: "ar",
    name: "العربية",
    flag: "🇸🇦",
  },
  {
    code: "da",
    name: "Dansk",
    flag: "🇩🇰",
  },
  {
    code: "es",
    name: "Español",
    flag: "🇪🇸",
  },
] as const;

interface LanguageSelectorProps {
  className?: string;
}

export function LanguageSelector({ className }: LanguageSelectorProps) {
  const locale = useLocale();
  const router = useRouter();
  const pathname = usePathname();

  const currentLanguage =
    languages.find((lang) => lang.code === locale) || languages[0];

  const handleLanguageChange = (languageCode: string) => {
    // 移除当前语言前缀并添加新的语言前缀
    const segments = pathname.split("/").filter(Boolean);
    const isCurrentLocaleInPath = segments[0] === locale;

    let newPath: string;
    if (isCurrentLocaleInPath) {
      // 替换现有的语言前缀
      segments[0] = languageCode;
      newPath = "/" + segments.join("/");
    } else {
      // 添加语言前缀
      newPath = `/${languageCode}${pathname}`;
    }

    router.push(newPath);
  };

  return (
    <DropdownMenu>
      <DropdownMenuTrigger
        className={cn(
          "w-48 cursor-pointer flex items-center justify-between gap-2 rounded-lg border border-gray-200 bg-white px-4 py-1 text-sm font-medium text-gray-700 shadow-sm transition-colors hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2",
          className
        )}
      >
        <div className="space-x-2">
          <span className="text-lg">{currentLanguage.flag}</span>
          <span>{currentLanguage.name}</span>
        </div>
        <ChevronsUpDown className="h-4 w-4 text-gray-400" />
      </DropdownMenuTrigger>

      <DropdownMenuContent
        align="end"
        className="w-48 rounded-lg border border-gray-200 bg-white p-1 shadow-lg"
      >
        {languages.map((language) => {
          const isSelected = language.code === locale;

          return (
            <DropdownMenuItem
              key={language.code}
              onClick={() => handleLanguageChange(language.code)}
              className={cn(
                "flex items-center gap-3 rounded-md px-3 py-2 text-sm cursor-pointer transition-colors",
                isSelected
                  ? "bg-indigo-600 text-white"
                  : "text-gray-700 hover:bg-gray-100"
              )}
            >
              <span className="text-lg">{language.flag}</span>
              <span className="flex-1">{language.name}</span>
              {isSelected && <CheckIcon className="h-4 w-4 text-white" />}
            </DropdownMenuItem>
          );
        })}
      </DropdownMenuContent>
    </DropdownMenu>
  );
}
