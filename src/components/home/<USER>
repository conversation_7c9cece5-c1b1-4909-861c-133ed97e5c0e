export default function HowItWorksSection() {
  const steps = [
    {
      icon: "/home/<USER>",
      title: "Upload your photos",
      description:
        "Upload clear, front-facing photos of both parents or partners into the generator for the most accurate results. Then choose the desired baby gender.",
    },
    {
      icon: "/home/<USER>",
      title: "Unleash the AI magic",
      description:
        "Activate our AI with a single tap on the “Generate” button, seamlessly blending features from both photos using cutting-edge algorithms.",
    },
    {
      icon: "/home/<USER>",
      title: "Meet your future child",
      description:
        "In moments, you’ll see an image of your future baby. You can download the photo, and share it with your loved ones.",
    },
  ];

  return (
    <section className="py-10 sm:py-16 lg:py-20">
      <div className="px-4 mx-auto max-w-7xl sm:px-6 lg:px-8">
        <div className="max-w-2xl mx-auto text-center">
          <h2 className="text-3xl font-bold leading-tight text-black sm:text-3xl lg:text-4xl">
            How our AI baby generator works?
          </h2>
        </div>
        <ul className="max-w-md mx-auto mt-16">
          {steps.map((step, index) => (
            <li key={index} className="relative flex items-start">
              {index < steps.length - 1 && (
                <div
                  className="absolute mt-0.5 top-14 left-8 w-px border-l-4 border-dotted border-gray-300 h-[calc(100%-3.5rem)]"
                  aria-hidden="true"
                />
              )}
              <div className="relative flex items-center justify-center flex-shrink-0 w-16 h-16 bg-white rounded-full shadow">
                <img
                  src={step.icon}
                  alt={step.title}
                  className="text-amber-500 w-10 h-10"
                />
              </div>
              <div className="ml-6 mb-8">
                <h3 className="text-lg font-semibold text-black">
                  {step.title}
                </h3>
                <p className="mt-4 text-base text-gray-600">
                  {step.description}
                </p>
              </div>
            </li>
          ))}
        </ul>
      </div>
    </section>
  );
}
