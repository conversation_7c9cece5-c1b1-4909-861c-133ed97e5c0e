"use client";

import * as React from "react";
import { Star } from "lucide-react";
import { cn } from "@/lib/utils";

interface TestimonialsSectionProps {
  className?: string;
}

interface TestimonialCardProps {
  name: string;
  username: string;
  avatar: string;
  rating: number;
  content: string;
}

function TestimonialCard({
  name,
  username,
  avatar,
  rating,
  content,
}: TestimonialCardProps) {
  return (
    <div className="overflow-hidden bg-white rounded-md">
      <div className="px-5 py-6">
        <div className="flex items-center justify-between">
          <img
            className="flex-shrink-0 object-cover w-10 h-10 rounded-full"
            src={avatar}
            alt="user profile"
            loading="lazy"
          />
          <div className="min-w-0 ml-3 mr-auto">
            <p className="text-base font-semibold text-black truncate">
              {name}
            </p>
            <p className="text-sm text-gray-600 truncate">{username}</p>
          </div>
        </div>

        <div className="flex items-center mt-4">
          {[...Array(5)].map((_, index) => (
            <Star
              key={index}
              className={cn(
                "w-4 h-4 ms-1",
                index < rating
                  ? "text-yellow-300 fill-current"
                  : "text-gray-300 fill-current"
              )}
              aria-hidden="true"
            />
          ))}
        </div>

        <blockquote className="mt-5">
          <p className="text-base text-gray-800">{content}</p>
        </blockquote>
      </div>
    </div>
  );
}

export function TestimonialsSection({ className }: TestimonialsSectionProps) {
  const testimonials = [
    {
      name: "Alexander Wright",
      username: "@alexanderw",
      avatar: "/home/<USER>",
      rating: 5,
      content:
        "Using this AI was a delightful experience. The baby photos generated were incredibly lifelike, capturing features from both my wife and me. It's a fun way to imagine our future family!",
    },
    {
      name: "Liam Turner",
      username: "@liamturner",
      avatar: "/home/<USER>",
      rating: 5,
      content:
        "I was skeptical at first, but this AI baby generator exceeded my expectations. The photos are so realistic, it's uncanny. Highly recommend!",
    },
    {
      name: "Grace Adams",
      username: "@graceadams",
      avatar: "/home/<USER>",
      rating: 4,
      content:
        "My husband and I were pleasantly surprised by the charm of the AI-generated baby photos. The process was straightforward, and the results were heartwarming. It would be great to have more customization options to tweak facial features.",
    },
    {
      name: "Marvin McKinney",
      username: "@marvinm",
      avatar: "/home/<USER>",
      rating: 5,
      content:
        "The process is easy, and the results are impressive. I can't wait to show my partner the photos!",
    },
  ];

  return (
    <section className={cn("py-10 bg-amber-50 sm:py-16 lg:py-20", className)}>
      <div className="px-4 mx-auto max-w-7xl sm:px-6 lg:px-8">
        <div className="max-w-2xl mx-auto text-center">
          <h2 className="text-3xl font-bold leading-tight text-black sm:text-3xl lg:text-4xl">
            Join 3,000+ happy users and meet your AI baby through the most
            realistic photos!
          </h2>
        </div>

        <div className="grid grid-cols-1 gap-6 px-4 mt-12 sm:px-0 xl:mt-20 xl:grid-cols-4 sm:grid-cols-2">
          {testimonials.map((testimonial, index) => (
            <TestimonialCard
              key={index}
              name={testimonial.name}
              username={testimonial.username}
              avatar={testimonial.avatar}
              rating={testimonial.rating}
              content={testimonial.content}
            />
          ))}
        </div>
      </div>
    </section>
  );
}
