import { cn } from "@/lib/utils";

export default function FeaturesSection() {
  const features = [
    {
      icon: "/home/<USER>",
      title: "Privacy is Our #1 Priority",
      description:
        "Your privacy is our top concern. Uploaded and generated images are promptly deleted after delivery.",
      bgColor: "bg-blue-100",
      iconColor: "text-blue-600",
    },
    {
      icon: "/home/<USER>",
      title: "Almost Instant Delivery",
      description:
        "Generate your future baby instantly, without any delay. High-quality results in just a few seconds.",
      bgColor: "bg-orange-100",
      iconColor: "text-orange-600",
    },
    {
      icon: "/home/<USER>",
      title: "Newly Released AI model",
      description:
        "Use our newly developed AI algorithms to create personalized baby images, based on the unique features you provide.",
      bgColor: "bg-green-100",
      iconColor: "text-green-600",
    },
    {
      icon: "/home/<USER>",
      title: "Own the Photos",
      description:
        "The images you generate are yours alone to keep and share, free to use anywhere.",
      bgColor: "bg-red-100",
      iconColor: "text-red-600",
    },
  ];

  return (
    <section className="py-10 bg-white sm:py-16 lg:py-20">
      <div className="px-4 mx-auto max-w-7xl sm:px-6 lg:px-8">
        <div className="grid grid-cols-1 text-center sm:grid-cols-2 gap-y-8 lg:grid-cols-4 sm:gap-12">
          {features.map((feature, index) => (
            <div key={index} className="flex flex-col items-center">
              <div
                className={`flex items-center justify-center w-20 h-20 mx-auto ${feature.bgColor} rounded-full`}
              >
                <img
                  src={feature.icon}
                  alt={`${feature.title} icon`}
                  className={cn(feature.iconColor, "w-9 h-9")}
                />
              </div>
              <h3 className="mt-8 text-lg font-semibold text-black">
                {feature.title}
              </h3>
              <p className="mt-4 text-sm text-gray-600">
                {feature.description}
              </p>
            </div>
          ))}
        </div>
      </div>
    </section>
  );
}
