"use client";

import { ROUTE_PATH } from "@/constant";
import { Link } from "@/i18n/navigation";

export default function JoinSection() {
  return (
    <section className="py-10 bg-white sm:py-16 lg:py-20">
      <div className="px-4 mx-auto sm:px-6 lg:px-8 max-w-7xl">
        <div className="max-w-2xl mx-auto text-center">
          <div className="flex items-center justify-center">
            <div className="w-20 h-20 -mr-6 overflow-hidden bg-gray-300 rounded-full">
              <img
                className="object-cover w-full h-full"
                src="/home/<USER>"
                alt=""
              />
            </div>
            <div className="relative overflow-hidden bg-gray-300 border-8 border-white rounded-full w-28 h-28">
              <img
                className="object-cover w-full h-full"
                src="/home/<USER>"
                alt=""
              />
            </div>
            <div className="w-20 h-20 -ml-6 overflow-hidden bg-gray-300 rounded-full">
              <img
                className="object-cover w-full h-full"
                src="/home/<USER>"
                alt=""
              />
            </div>
          </div>
          <h2 className="mt-8 text-3xl font-bold leading-tight text-black lg:mt-12 sm:text-4xl lg:text-5xl">
            Join <span className="border-b-8 border-yellow-300">3,182</span>{" "}
            other happy users
          </h2>
          <p className="max-w-xl mx-auto mt-6 text-xl text-gray-600 md:mt-10">
            Don&apos;t waste your time guessing “what would my baby look like?”.
            Try our AI Baby Generator today and see your future in a whole new
            way!
          </p>
          <Link
            href={ROUTE_PATH.GENERATOR}
            className="inline-flex items-center justify-center px-6 py-4 mt-8 font-semibold text-black transition-all duration-200 bg-yellow-300 rounded-full lg:mt-16 hover:bg-yellow-400 focus:bg-yellow-400"
            role="button"
          >
            <img src="/home/<USER>" className="w-5 h-5" />
            Meet My Baby Now
          </Link>
        </div>
      </div>
    </section>
  );
}
