"use client";

import * as React from "react";
import { ArrowRight } from "lucide-react";

import { cn } from "@/lib/utils";
import { ROUTE_PATH } from "@/constant";
import { Link } from "@/i18n/navigation";

interface HeroSectionProps {
  className?: string;
}

export function HeroSection({ className }: HeroSectionProps) {
  return (
    <section className={cn("py-10 sm:py-8 lg:py-10", className)}>
      <div className="px-4 mx-auto max-w-7xl sm:px-6 lg:px-8">
        <div className="grid items-center grid-cols-1 gap-12 lg:grid-cols-2">
          {/* Left Content */}
          <div>
            <h1 className="mt-4 text-4xl font-bold text-black lg:mt-8 sm:text-6xl xl:text-7xl">
              Free AI Baby Generator: <br className="hidden sm:block" />
              See Your Future Baby In One Click
            </h1>

            <div className="mt-4 text-base !leading-9 text-black lg:mt-8 sm:text-xl">
              <p className="flex items-start gap-2">
                <span>🏆</span>
                <span>#1 Baby Generator powered by newly released AI</span>
              </p>
              <p className="flex items-start gap-2">
                <span>⚡️</span>
                <span>
                  Ultra-realistic baby photos delivered almost instantly
                </span>
              </p>
              <p className="flex items-start gap-2">
                <span>👶</span>
                <span>12,000+ photos delivered to 3000+ families</span>
              </p>
            </div>

            <Link
              href={ROUTE_PATH.GENERATOR}
              className="inline-flex items-center px-6 py-4 mt-8 font-semibold text-black transition-all duration-200 bg-yellow-300 rounded-full lg:mt-16 hover:bg-yellow-400 focus:bg-yellow-400 focus:outline-none focus:ring-2 focus:ring-yellow-500 focus:ring-offset-2"
              title="Generate your baby photo"
              role="button"
            >
              Meet Your Baby Now
              <ArrowRight className="w-6 h-6 ml-8 -mr-2" />
            </Link>

            <p className="mt-5 text-gray-600">
              Already joined us?{" "}
              <Link
                href={ROUTE_PATH.LOGIN}
                className="text-amber-600 transition-all duration-200 hover:underline focus:underline focus:outline-none"
                title="Log in to your account"
              >
                Log in
              </Link>
            </p>
          </div>

          {/* Right Content - Hero Image */}
          <div>
            <img
              className="w-full"
              src="/home/<USER>"
              alt="Example of a Couple's AI Generated Child using AI Baby Generator"
              loading="eager"
            />
          </div>
        </div>
      </div>
    </section>
  );
}
