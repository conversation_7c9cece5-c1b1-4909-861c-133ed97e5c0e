"use client";

import {
  Accordion,
  Accordion<PERSON>ontent,
  AccordionItem,
  AccordionTrigger,
} from "@/components/ui/accordion";
import { ROUTE_PATH } from "@/constant";
import { Link } from "@/i18n/navigation";
import { cn } from "@/lib/utils";

export default function FAQSection() {
  const faqs = [
    {
      question: "What type of photos should I upload?",
      answer:
        "Upload clear, high-resolution photos of yourself or your family for the best results.",
    },
    {
      question: "How long does it take to generate a photo of my baby?",
      answer: "Most photos are generated in less than 20 seconds.",
    },
    {
      question: "Where is my data stored?",
      answer:
        "Your data is securely stored on our encrypted cloud servers, adhering to strict privacy standards.",
    },
    {
      question: "Can I delete my data?",
      answer:
        "Yes, you can delete your data at any time through your account settings.",
    },
    {
      question: "Can I use the photos anywhere?",
      answer:
        "You can use the generated photos for personal use, but commercial use may require additional permissions.",
    },
    {
      question: "What do you do with my photos?",
      answer:
        "Your photos are used solely for generating the requested images and are not shared or used for other purposes.",
    },
    {
      question: "How does this work?",
      answer:
        "Our AI analyzes your uploaded photos and uses advanced algorithms to generate realistic images based on them.",
    },
    {
      question: "Is my privacy protected?",
      answer:
        "Yes, we prioritize your privacy with end-to-end encryption and strict data protection policies.",
    },
    {
      question: "Can I cancel my subscription?",
      answer:
        "You can cancel your subscription at any time via your account dashboard.",
    },
  ];

  return (
    <section className="py-10 bg-gray-50 sm:py-16 lg:py-20">
      <div className="px-4 mx-auto sm:px-6 lg:px-8 max-w-7xl">
        <div className="max-w-2xl mx-auto text-center">
          <h2 className="text-3xl font-bold leading-tight text-black sm:text-3xl lg:text-4xl">
            Frequently Asked Questions
          </h2>
          <p className="max-w-xl mx-auto mt-4 text-base leading-relaxed text-gray-600">
            Find answers to common questions about our service.
          </p>
        </div>
        <div className="max-w-3xl mx-auto mt-8 space-y-4 md:mt-16">
          <Accordion type="single" collapsible className="w-full">
            {faqs.map((faq, index) => (
              <AccordionItem
                key={index}
                value={`item-${index}`}
                className="transition-all duration-200 bg-white border border-gray-200 shadow-lg cursor-pointer hover:bg-gray-50"
              >
                <AccordionTrigger
                  className={cn(
                    "flex items-center justify-between w-full px-4 py-5 sm:p-6",
                    "[&[data-state=open]]:bg-gray-50"
                  )}
                >
                  <span className="flex text-lg font-semibold text-black">
                    {faq.question}
                  </span>
                </AccordionTrigger>
                <AccordionContent className="px-4 pb-5 sm:px-6 sm:pb-6 text-gray-600">
                  {faq.answer}
                </AccordionContent>
              </AccordionItem>
            ))}
          </Accordion>
        </div>
        <p className="text-center text-gray-600 text-base mt-9">
          Didn&apos;t find the answer you are looking for?{" "}
          <Link
            href={ROUTE_PATH.SUPPORT}
            className="font-medium text-amber-600 transition-all duration-200 hover:text-blue-700 focus:text-blue-700 hover:no-underline"
          >
            Contact our support
          </Link>
        </p>
      </div>
    </section>
  );
}
