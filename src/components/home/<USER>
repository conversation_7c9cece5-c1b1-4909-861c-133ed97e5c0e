"use client";

export default function WhySection() {
  return (
    <section className="py-10 px-2 bg-amber-50 sm:py-16 lg:py-20">
      <div className="max-w-7xl px-1 mx-auto sm:px-1 lg:px-1">
        <div className="grid items-center grid-cols-1 gap-y-6 md:grid-cols-2 md:gap-x-20">
          <div className="px-1 md:*:px-0">
            <h2 className="text-3xl font-bold leading-tight text-black sm:text-3xl lg:text-4xl">
              Why did we make AI Baby Generator?
            </h2>
            <p className="mt-8 text-xl !leading-8 text-gray-600">
              At the heart of our AI baby generator lies a vision fueled by a
              blend of technology and the warmth of family love. We embarked on
              this journey with a singular purpose: to infuse AI innovation with
              the joy and closeness that define family bonds. <br /> <br /> Our
              goal was not just to create a tool, but to craft an experience
              that celebrates life&apos;s most precious moments. This AI
              isn&apos;t just about baby look predictions; it&apos;s about
              creating smiles, sparking imaginations, and enriching the journey
              with loved ones.
            </p>
          </div>
          <div className="relative pl-20 pr-6 sm:pl-6 md:px-0">
            <div className="relative w-full max-w-xs mt-4 mb-10 ml-auto">
              <img
                className="ml-auto"
                src="/home/<USER>"
                alt="Example of a loving Couple's AI Generated Child using AI Baby Generator"
              />
            </div>
          </div>
        </div>
      </div>
    </section>
  );
}
