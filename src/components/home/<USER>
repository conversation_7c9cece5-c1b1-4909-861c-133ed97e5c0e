"use client";

import * as React from "react";
import { cn } from "@/lib/utils";
import { ROUTE_PATH } from "@/constant";
import { Link } from "@/i18n/navigation";

interface FeatureSectionProps {
  className?: string;
}

export function FeatureSection({ className }: FeatureSectionProps) {
  return (
    <section className={cn("py-10 px-2 bg-white sm:py-16 lg:py-10", className)}>
      <div className="max-w-7xl mx-auto">
        <div className="grid items-center md:grid-cols-2 md:gap-x-20 gap-y-10 mx-auto">
          {/* Left Content - Image */}
          <div className="px-1 md:px-0">
            <div className="pr-12 sm:pr-0">
              <div className="relative max-w-xl mb-12">
                <img
                  className="object-bottom rounded-md w-full"
                  src="/home/<USER>"
                  alt="Example of a Couple's AI Generated Child using AI Baby Generator"
                  loading="lazy"
                />
              </div>
            </div>
          </div>

          {/* Right Content - Text */}
          <div className="px-1 md:px-0">
            <h2 className="text-3xl font-bold leading-tight text-black sm:text-3xl lg:text-4xl">
              Unique AI-powered baby generator gives a glimpse of your future
              child
            </h2>

            <div className="mt-12 text-xl !leading-7 text-gray-600">
              <p>
                Have you ever wondered, &quot;What will our baby look
                like?&quot; Now, you can satisfy that curiosity with our
                cutting-edge AI Baby Generator! Our advanced Baby Mystic AI
                model is designed to generate{" "}
                <span className="font-bold text-transparent bg-clip-text bg-gradient-to-r from-cyan-500 to-amber-500">
                  highly realistic images
                </span>{" "}
                of babies by combining the characteristics of both parents.
              </p>

              <br />
              <br />

              <p>
                Our unique technology, fine-tuned with{" "}
                <span className="font-bold text-transparent bg-clip-text bg-gradient-to-r from-cyan-500 to-amber-500">
                  a dataset of 4 million internal corporate images
                </span>
                , ensures that the AI-generated baby closely mirrors both your
                features. Simply upload your and your partner&apos;s photos to
                our baby generator and witness the magic as our AI brings your
                future baby to life.
              </p>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
}

interface FeatureSectionTwoProps {
  className?: string;
}

export function FeatureSection2({ className }: FeatureSectionTwoProps) {
  return (
    <section className={cn("py-10 px-2 bg-white sm:py-16 lg:py-10", className)}>
      <div className="max-w-7xl px-1 mx-auto sm:px-1 lg:px-1">
        <div className="grid items-center md:grid-cols-2 md:gap-x-20 gap-y-10 mx-auto">
          {/* Right Content - Image */}
          <div className="relative pl-12 pr-10 sm:pl-6 md:pl-0 xl:pr-0 md:order-2">
            <div className="relative max-w-xs ml-auto">
              <div className="overflow-hidden aspect-w-3 aspect-h-4">
                <img
                  className="object-cover w-full h-full"
                  src="/home/<USER>"
                  alt="Image showing how Our AI Baby Generator would analyze facial features"
                  loading="lazy"
                />
              </div>
            </div>
          </div>

          {/* Left Content - Text */}
          <div className="md:order-1 px-1 md:px-0">
            <h2 className="text-3xl font-bold leading-tight text-black sm:text-3xl lg:text-4xl">
              Fully personalized AI baby face prediction with no waiting time
            </h2>

            <p className="mt-16 text-xl !leading-7 text-gray-600">
              Our baby generator analyzes up to{" "}
              <span className="font-bold mt-8 text-xl !leading-7 text-transparent bg-clip-text bg-gradient-to-r from-cyan-500 to-amber-500">
                70 unique facial features
              </span>{" "}
              of babies by combining the characteristics of both parents.
              <br />
              <br />
              Experience the excitement of seeing a lifelike representation of
              your future child&apos;s look with our revolutionary AI Baby
              Generator. Try it today and let our AI transform your imagination
              into reality!
            </p>

            <Link
              href={ROUTE_PATH.GENERATOR}
              className="inline-flex items-center justify-center px-8 py-3 mt-8 text-base font-semibold text-black transition-all duration-200 bg-yellow-300 rounded-full lg:mt-16 hover:bg-yellow-400 focus:bg-yellow-400"
              title="Generate your baby photo"
              role="button"
            >
              Meet Your Baby Now
            </Link>
          </div>
        </div>
      </div>
    </section>
  );
}
