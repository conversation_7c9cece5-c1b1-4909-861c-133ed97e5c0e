"use client";

import { useForm } from "react-hook-form";
import { z } from "zod";

import { zodResolver } from "@hookform/resolvers/zod";
import { Link } from "@/i18n/navigation";
import { But<PERSON> } from "@/components/ui/button";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";

const formSchema = z.object({
  email: z
    .string()
    .min(1, "Email is required")
    .email("Please enter a valid email address"),
  password: z
    .string()
    .min(8, "Password must be at least 8 characters")
    .max(100, "Password cannot exceed 100 characters"),
});

type FormData = z.infer<typeof formSchema>;

export default function LoginForm() {
  const form = useForm<FormData>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      email: "",
      password: "",
    },
  });

  const onSubmit = async (data: FormData) => {
    console.log("data", data);
  };

  return (
    <div className="pt-6 sm:pt-10">
      <div className="flex min-h-full flex-col justify-center py-2 sm:py-4 px-4 sm:px-6 lg:px-8 2xl:py-10">
        <div className="mx-auto w-full max-w-md">
          <h2 className="mt-2 text-center text-xl sm:text-2xl font-bold leading-9 tracking-tight text-gray-900 lg:mt-6">
            Sign in to your account
          </h2>
        </div>
        <div className="mt-6 sm:mt-10 mx-auto w-full max-w-[90%] sm:max-w-120">
          <div className="bg-white px-4 py-8 sm:px-6 sm:py-12 shadow sm:rounded-lg">
            <div className="my-3 flex items-center justify-center">
              <Button
                variant="outline"
                className="cursor-pointer h-10 sm:h-11 w-full max-w-[250px] sm:max-w-[275px] border-gray-300 text-gray-900 text-sm sm:text-base"
                onClick={() => {}}
              >
                <img
                  src="google.svg"
                  alt="Google logo"
                  className="w-4 h-4 mr-2"
                />
                Sign in with Google
              </Button>
            </div>
            <Form {...form}>
              <form
                onSubmit={form.handleSubmit(onSubmit)}
                className="space-y-4 sm:space-y-6"
              >
                <FormField
                  control={form.control}
                  name="email"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel className="block text-sm font-medium leading-6 text-gray-900">
                        Email address
                      </FormLabel>
                      <FormControl>
                        <Input
                          id="email"
                          type="email"
                          autoComplete="email"
                          className="block w-full rounded-md border-0 py-1.5 pl-2 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-indigo-600 text-sm sm:text-base sm:leading-6"
                          {...field}
                        />
                      </FormControl>
                      <FormMessage className="mt-1 text-sm text-red-600" />
                    </FormItem>
                  )}
                />
                <FormField
                  control={form.control}
                  name="password"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel className="block text-sm font-medium leading-6 text-gray-900">
                        Password
                      </FormLabel>
                      <FormControl>
                        <Input
                          id="password"
                          type="password"
                          autoComplete="current-password"
                          className="block w-full rounded-md border-0 py-1.5 pl-2 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-indigo-600 text-sm sm:text-base sm:leading-6"
                          {...field}
                        />
                      </FormControl>
                      <FormMessage className="mt-1 text-sm text-red-600" />
                    </FormItem>
                  )}
                />
                <div className="flex items-center justify-end">
                  <div className="text-sm leading-6">
                    <Link
                      href="#"
                      className="font-semibold text-amber-500 hover:text-indigo-500"
                    >
                      Forgot password?
                    </Link>
                  </div>
                </div>
                <div>
                  <Button
                    type="submit"
                    disabled={form.formState.isSubmitting}
                    className="flex w-full items-center justify-center rounded-md bg-yellow-300 px-3 py-1.5 text-sm font-semibold leading-6 text-black shadow-sm hover:bg-yellow-400 focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2 disabled:opacity-50"
                  >
                    {form.formState.isSubmitting && (
                      <svg
                        className="h-4 w-4 sm:h-5 sm:w-5 -ml-2 mr-2 animate-spin"
                        viewBox="0 0 24 24"
                        fill="none"
                        xmlns="http://www.w3.org/2000/svg"
                      >
                        <circle
                          className="opacity-25"
                          cx="12"
                          cy="12"
                          r="10"
                          stroke="currentColor"
                          strokeWidth="4"
                        />
                        <path
                          className="opacity-75"
                          fill="currentColor"
                          d="M4 12a8 8 0 018-8v4a4 4 0 00-4 4H4z"
                        />
                      </svg>
                    )}
                    Sign in
                  </Button>
                </div>
              </form>
            </Form>
            <div className="relative mt-6 sm:mt-10 flex flex-col justify-center">
              <div className="text-center text-sm sm:text-base">
                Don&apos;t have an account?{" "}
                <Link
                  href="#"
                  className="ml-1 font-bold text-amber-500 hover:text-amber-600"
                >
                  Sign up
                </Link>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
