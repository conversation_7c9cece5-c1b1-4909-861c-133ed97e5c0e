"use client";

import * as React from "react";
import { <PERSON><PERSON>, DialogContent, DialogTrigger } from "@/components/ui/dialog";
import LoginForm from "@/components/LoginForm";

interface LoginDialogProps {
  children: React.ReactNode;
}

export function LoginDialog({ children }: LoginDialogProps) {
  const [open, setOpen] = React.useState(false);

  return (
    <Dialog open={open} onOpenChange={setOpen}>
      <DialogTrigger asChild>{children}</DialogTrigger>
      <DialogContent className="sm:max-w-[500px] p-0 overflow-hidden">
        <LoginForm />
      </DialogContent>
    </Dialog>
  );
}
