"use client";

import * as React from "react";
import { Menu } from "lucide-react";

import { But<PERSON> } from "@/components/ui/button";
import { LanguageSelector } from "@/components/language-selector";
import { cn } from "@/lib/utils";
import { ROUTE_PATH } from "@/constant";
import { Link } from "@/i18n/navigation";

interface SiteHeaderProps {
  className?: string;
}

export function AppHeader({ className }: SiteHeaderProps) {
  const [isMobileMenuOpen, setIsMobileMenuOpen] = React.useState(false);

  const navigationItems = [
    { href: ROUTE_PATH.HOME, label: "Home" },
    { href: ROUTE_PATH.BABY_NAME_GENERATOR, label: "Baby Name Generator" },
    { href: ROUTE_PATH.FAQ, label: "FAQ" },
    { href: ROUTE_PATH.SUPPORT, label: "Support" },
    {
      href: ROUTE_PATH.PRICING,
      label: "Pricing",
    },
  ];

  return (
    <header
      className={cn(
        "w-full sticky top-0 bg-amber-50 text-amber-500 z-10 h-20",
        className
      )}
    >
      <nav
        className="mx-auto flex items-center justify-between p-4 lg:px-8"
        aria-label="Global"
      >
        {/* Left side - Logo and Navigation */}
        <div className="flex items-center gap-x-10 2xl:gap-x-20">
          {/* Logo */}
          <Link
            href={ROUTE_PATH.HOME}
            className="-m-1.5 p-1.5 flex items-center justify-center"
          >
            <span className="sr-only">AI Baby Generator</span>
            <img
              className="mr-3 h-12"
              src="/logo.webp"
              alt="AI Baby Generator logo"
            />
            <h1 className="text-2xl font-semibold text-nowrap">
              AI Baby Generator
            </h1>
          </Link>

          {/* Desktop Navigation */}
          <div className="hidden lg:flex lg:gap-x-8 2xl:gap-x-12">
            {navigationItems.map((item) => (
              <Link
                key={item.href}
                href={item.href}
                className="text-sm font-semibold leading-6 text-gray-600 text-nowrap hover:text-gray-900 transition-colors"
                {...(item.href.startsWith("http") && {
                  target: "_blank",
                  rel: "noopener noreferrer",
                })}
              >
                {item.label}
              </Link>
            ))}
          </div>
        </div>

        {/* Mobile menu button */}
        <div className="flex lg:hidden">
          <Button
            variant="ghost"
            size="icon"
            onClick={() => setIsMobileMenuOpen(!isMobileMenuOpen)}
            className="-m-2.5 inline-flex items-center justify-center rounded-md p-2.5 text-gray-600"
          >
            <span className="sr-only">Open main menu</span>
            <Menu className="h-6 w-6" />
          </Button>
        </div>

        {/* Right side - Language selector and Login button */}
        <div className="hidden lg:flex items-center">
          <div className="mx-10">
            <LanguageSelector />
          </div>
          <div>
            <Button className="text-sm font-semibold text-gray-700 bg-yellow-300 px-5 hover:bg-yellow-400 py-1.5 rounded-lg leading-6">
              Log in
            </Button>
          </div>
        </div>
      </nav>

      {/* Mobile Navigation Menu */}
      {isMobileMenuOpen && (
        <div className="lg:hidden text-black">
          <div className="fixed inset-0 z-10"></div>
          <div className="fixed inset-y-0 right-0 z-10 w-full overflow-y-auto bg-white px-6 py-6 sm:max-w-sm">
            {/* Mobile Header */}
            <div className="flex items-center justify-between text-amber-500">
              <Link
                href={ROUTE_PATH.HOME}
                className="-m-1.5 p-1.5 flex items-center justify-center"
              >
                <span className="sr-only">AI Baby Generator</span>
                <img
                  className="mr-3 h-12"
                  src="/logo.webp"
                  alt="AI Baby Generator logo"
                />
                <p className="text-2xl font-semibold text-nowrap">
                  AI Baby Generator
                </p>
              </Link>
              <Button
                variant="ghost"
                size="icon"
                onClick={() => setIsMobileMenuOpen(false)}
                className="-m-2.5 rounded-md p-2.5 text-black"
              >
                <span className="sr-only">Close menu</span>
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  fill="none"
                  viewBox="0 0 24 24"
                  strokeWidth="1.5"
                  stroke="currentColor"
                  className="h-6 w-6"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    d="M6 18 18 6M6 6l12 12"
                  />
                </svg>
              </Button>
            </div>

            {/* Mobile Menu Content */}
            <div className="mt-6 flow-root">
              <div className="-my-6 divide-y divide-gray-500/10">
                <div className="space-y-2 py-6">
                  {/* Language Selector */}
                  <div className="flex items-center mb-4">
                    <LanguageSelector />
                  </div>

                  {/* Navigation Links */}
                  {navigationItems.map((item) => (
                    <Link
                      key={item.href}
                      href={item.href}
                      className="-mx-3 block rounded-lg px-3 py-2 text-base font-semibold leading-7 hover:bg-gray-50 text-nowrap"
                      onClick={() => setIsMobileMenuOpen(false)}
                      {...(item.href.startsWith("http") && {
                        target: "_blank",
                        rel: "noopener noreferrer",
                      })}
                    >
                      {item.label}
                    </Link>
                  ))}
                </div>
              </div>
            </div>
          </div>
        </div>
      )}
    </header>
  );
}
