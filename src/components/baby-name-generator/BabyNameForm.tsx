"use client";

import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import * as z from "zod";

import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
} from "@/components/ui/form";

const formSchema = z.object({
  fatherName: z.string().optional(),
  motherName: z.string().optional(),
  gender: z.enum(["Boy", "Girl", "Neutral"], {
    required_error: "Please select a gender",
  }),
  specificLanguage: z.string().optional(),
});

type FormData = z.infer<typeof formSchema>;

export default function BabyNameForm() {
  const form = useForm<FormData>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      fatherName: "",
      motherName: "",
      gender: "" as "Boy" | "Girl" | "Neutral",
      specificLanguage: "any",
    },
  });

  const onSubmit = (data: FormData) => {
    console.log("Form submitted:", data);
  };

  return (
    <div className="container max-w-4xl mx-auto p-4">
      <h1
        id="baby_name_generator"
        className="text-4xl md:text-5xl font-bold text-center text-transparent bg-clip-text bg-gradient-to-r from-amber-500 to-yellow-600 mb-6"
      >
        Baby Name Generator
      </h1>
      <p className="text-gray-700 text-center mb-10">
        Select your preferences, and we will generate the perfect name for your
        baby
      </p>
      <div className="bg-white/90 backdrop-blur-sm rounded-xl shadow-lg p-6 mb-8 border border-amber-200">
        <h2 className="text-2xl font-bold text-center mb-6 text-amber-700">
          Pick your preferences
        </h2>
        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
            <FormField
              control={form.control}
              name="fatherName"
              render={({ field }) => (
                <FormItem>
                  <FormLabel className="block text-gray-700 font-medium mb-2">
                    Father Name (Optional)
                  </FormLabel>
                  <FormControl>
                    <Input
                      {...field}
                      placeholder="Father's name"
                      className="w-full px-4 py-3 border border-amber-200 rounded-lg focus:outline-none focus:ring-2 focus:ring-amber-400 focus:border-transparent bg-[#FFFDF7]"
                    />
                  </FormControl>
                </FormItem>
              )}
            />
            <FormField
              control={form.control}
              name="motherName"
              render={({ field }) => (
                <FormItem>
                  <FormLabel className="block text-gray-700 font-medium mb-2">
                    Mother Name (Optional)
                  </FormLabel>
                  <FormControl>
                    <Input
                      {...field}
                      placeholder="Mother's name"
                      className="w-full px-4 py-3 border border-amber-200 rounded-lg focus:outline-none focus:ring-2 focus:ring-amber-400 focus:border-transparent bg-[#FFFDF7]"
                    />
                  </FormControl>
                </FormItem>
              )}
            />
            <FormField
              control={form.control}
              name="gender"
              render={({ field }) => (
                <FormItem>
                  <FormLabel className="block text-gray-700 font-medium mb-2">
                    Gender
                  </FormLabel>
                  <FormControl>
                    <div className="flex space-x-4">
                      <Button
                        type="button"
                        variant={field.value === "Boy" ? "default" : "outline"}
                        className={`py-2 px-6 rounded-lg transition-all shadow-sm ${
                          field.value === "Boy"
                            ? "bg-blue-50 border-blue-300 text-blue-700 hover:bg-blue-100"
                            : "border-gray-200 hover:border-blue-300 bg-[#FFFDF7] text-gray-700"
                        }`}
                        onClick={() => field.onChange("Boy")}
                      >
                        Boy
                      </Button>
                      <Button
                        type="button"
                        variant={field.value === "Girl" ? "default" : "outline"}
                        className={`py-2 px-6 rounded-lg transition-all shadow-sm ${
                          field.value === "Girl"
                            ? "bg-pink-50 border-pink-300 text-pink-700 hover:bg-pink-100"
                            : "border-gray-200 hover:border-pink-300 bg-[#FFFDF7] text-gray-700"
                        }`}
                        onClick={() => field.onChange("Girl")}
                      >
                        Girl
                      </Button>
                      <Button
                        type="button"
                        variant={
                          field.value === "Neutral" ? "default" : "outline"
                        }
                        className={`py-2 px-6 rounded-lg transition-all shadow-sm ${
                          field.value === "Neutral"
                            ? "bg-purple-50 border-purple-300 text-purple-700 hover:bg-purple-100"
                            : "border-gray-200 hover:border-purple-300 bg-[#FFFDF7] text-gray-700"
                        }`}
                        onClick={() => field.onChange("Neutral")}
                      >
                        Neutral
                      </Button>
                    </div>
                  </FormControl>
                </FormItem>
              )}
            />
            <FormField
              control={form.control}
              name="specificLanguage"
              render={({ field }) => (
                <FormItem>
                  <FormLabel className="block text-gray-700 font-medium mb-2">
                    Specific Language (Optional)
                  </FormLabel>
                  <Select onValueChange={field.onChange} value={field.value}>
                    <FormControl>
                      <SelectTrigger className="w-full px-4 py-3 border border-amber-200 rounded-lg focus:outline-none focus:ring-2 focus:ring-amber-400 focus:border-transparent bg-[#FFFDF7]">
                        <SelectValue placeholder="Any 🌏" />
                      </SelectTrigger>
                    </FormControl>
                    <SelectContent>
                      <SelectItem value="any">Any 🌏</SelectItem>
                      <SelectItem value="en">English 🇬🇧</SelectItem>
                      <SelectItem value="fr">French 🇫🇷</SelectItem>
                      <SelectItem value="es">Spanish 🇪🇸</SelectItem>
                      <SelectItem value="de">German 🇩🇪</SelectItem>
                      <SelectItem value="it">Italian 🇮🇹</SelectItem>
                      <SelectItem value="jp">Japanese 🇯🇵</SelectItem>
                      <SelectItem value="ko">Korean 🇰🇷</SelectItem>
                      <SelectItem value="ru">Russian 🇷🇺</SelectItem>
                      <SelectItem value="zh">Chinese 🇨🇳</SelectItem>
                      <SelectItem value="ar">Arabic 🇸🇦</SelectItem>
                      <SelectItem value="hi">Hindi 🇮🇳</SelectItem>
                    </SelectContent>
                  </Select>
                </FormItem>
              )}
            />
            <div className="flex justify-center mt-8">
              <Button
                type="submit"
                disabled={!form.formState.isValid}
                className="px-8 py-3 bg-gradient-to-r from-amber-500 to-yellow-500 text-white font-medium rounded-lg shadow-lg transition-all hover:from-amber-600 hover:to-yellow-600 hover:shadow-xl disabled:opacity-70 disabled:cursor-not-allowed transform hover:scale-105"
              >
                Generate Names
              </Button>
            </div>
          </form>
        </Form>
      </div>
    </div>
  );
}
