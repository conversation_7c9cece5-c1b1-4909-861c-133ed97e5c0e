"use client";

import { Link } from "@/i18n/navigation";

const faqData = [
  {
    question: "How does the baby name generator work?",
    answer:
      "We use a database of names from around the world. When you select your preferences like gender, origin, or family names, our system finds options that match what you're looking for",
  },
  {
    question: "What information comes with each name?",
    answer:
      "For each name, we provide its meaning and cultural background. This helps you understand the story behind the name and whether it fits what you're looking for",
  },
  {
    question: "What if I don't like the suggestions?",
    answer:
      "That's totally fine. Finding the right name sometimes takes time. You can always generate more options or try different combinations of preferences until something clicks",
  },
  {
    question: "How many different cultures are represented?",
    answer:
      "We include names from many backgrounds including European (such as France, Germany), Asian (such as China, Japan), African (such as Egypt, Nigeria), Middle Eastern (such as Saudi Arabia, Iran), and Indigenous cultures",
  },
];

export default function BabyNameGeneratorDesc() {
  return (
    <div className="py-28">
      <div className="flex flex-col items-center justify-center">
        <h2 className="text-[#5d4037] text-4xl font-bold mb-6">
          AI-powered Baby Name Generator
        </h2>
        <p className="text-[#795548] text-xl mb-10 text-center max-w-5xl">
          Help you quickly find a suitable name for the baby
        </p>
        <div className="bg-white/80 rounded-xl p-8 max-w-4xl mx-auto mb-12 shadow-lg">
          <h3 className="text-2xl font-bold text-[#ff6f00] mb-4">
            Finding the Right Name
          </h3>
          <p className="text-gray-700 mb-4">
            Choosing a unique and meaningful baby name is one of the first
            important decisions you&apos;ll make as a parent. It&apos;s
            something your child will carry with them throughout their life
          </p>
          <p className="text-gray-700 mb-4">
            Our tool helps you explore options from different cultures and
            backgrounds. Just tell us what you&apos;re looking for, and
            we&apos;ll suggest names that might be a good fit for your family
          </p>
          <p className="text-gray-700">
            Whether you want something traditional, unique, or meaningful,
            we&apos;re here to help you find a name that feels right
          </p>
        </div>
        <div className="bg-white/80 rounded-xl p-8 max-w-4xl mx-auto shadow-lg">
          <h3 className="text-2xl font-bold text-[#ff6f00] mb-6">
            Common Questions
          </h3>
          {faqData.map((faq, index) => (
            <div key={index} className="mb-6">
              <h4 className="font-bold text-[#8d6e63] text-lg mb-2">
                {faq.question}
              </h4>
              <p className="text-gray-700">{faq.answer}</p>
            </div>
          ))}
        </div>
        <Link
          href="#baby_name_generator"
          className="mt-10 bg-[#ff9800] hover:bg-[#f57c00] text-white font-bold py-3 px-8 rounded-full shadow-lg transition duration-300 transform hover:scale-105"
        >
          Generate Baby Names Now
        </Link>
      </div>
    </div>
  );
}
