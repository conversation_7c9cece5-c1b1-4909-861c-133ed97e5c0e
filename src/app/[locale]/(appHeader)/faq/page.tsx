import { Link } from "@/i18n/navigation";

const faqData = [
  {
    question: "What type of photos should I upload?",
    answer:
      "High-quality photos with good lighting, showing visible and clear face. Only one person in each photo with natural expression.",
  },
  {
    question: "How long does it take to generate a photo of my baby?",
    answer: "Most photos are generated in less than 20 seconds.",
  },
  {
    question: "Where is my data stored?",
    answer:
      "Data is stored securely on servers in the United States, by vetted, highly secure, third-party partners. We delete all images within 1 day.",
  },
  {
    question: "Can I delete my data?",
    answer:
      "Yes, you can request deletion at any time by sending us an <NAME_EMAIL>.",
  },
  {
    question: "Can I use the photos anywhere?",
    answer:
      "Yes, you can use the baby photos anywhere you want. You can use them on your social media profiles and your website.",
  },
  {
    question: "What do you do with my photos?",
    answer:
      "Our AI predicts how your future child would look like based on your photos and render your future child's photos. After that, we delete your photos from our servers after delivery of your future baby photos.",
  },
  {
    question: "How does this work?",
    answer:
      "Our advanced AI technology crafts stunning images of your potential future child, blending features from your photos to create a heartwarming glimpse into what might be.",
  },
  {
    question: "Is my privacy protected?",
    answer:
      "Yes, your data is safe and protected with us, as we follow all the required compliances and do not share any of your data with any third party. You can also checkout our privacy policy.",
  },
  {
    question: "Can I cancel my subscription?",
    answer:
      "You can cancel your subscription anytime by emailing <NAME_EMAIL>. After the subscription is canceled, you will not be charged on the next billing cycle. You will continue to have the benefits of your current subscription until it expires.",
  },
];

export default function FAQSection() {
  return (
    <div className="mx-auto max-w-7xl px-6 py-6 sm:py-12 lg:px-8">
      <div className="mx-auto max-w-2xl text-center">
        <h1 className="text-2xl font-bold leading-10 tracking-tight text-gray-900">
          Frequently asked questions
        </h1>
        <p className="mt-6 text-base leading-7 text-gray-600">
          Have a different question and can&apos;t find the answer you&apos;re
          looking for? Reach out to our support team by{" "}
          <Link
            href="mailto:<EMAIL>?subject=Feedback&body=Body%20Content"
            className="font-semibold text-amber-600 hover:text-amber-500"
          >
            sending us an email
          </Link>{" "}
          and we&apos;ll get back to you as soon as we can.
        </p>
      </div>
      <div className="mx-auto max-w-7xl divide-y divide-gray-900/10 px-6 lg:px-8">
        <dl className="mt-10 divide-y divide-gray-900/10">
          {faqData.map((faq, index) => (
            <div key={index} className="py-6 lg:grid lg:grid-cols-12 lg:gap-8">
              <dt className="text-base font-semibold leading-7 text-gray-900 lg:col-span-5">
                {faq.question}
              </dt>
              <dd className="mt-4 lg:col-span-7 lg:mt-0">
                <p className="text-base leading-7 text-gray-600">
                  {faq.answer}
                </p>
              </dd>
            </div>
          ))}
        </dl>
      </div>
    </div>
  );
}
