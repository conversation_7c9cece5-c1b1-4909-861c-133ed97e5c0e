"use client";

import Image from "next/image";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import * as z from "zod";
import { Button } from "@/components/ui/button";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";

const formSchema = z.object({
  fatherImage: z
    .instanceof(File)
    .refine(
      (file) => file.size <= 5 * 1024 * 1024,
      "File size must be less than 5MB"
    )
    .refine(
      (file) => ["image/jpeg", "image/png", "image/webp"].includes(file.type),
      "Only JPEG, PNG, or WebP images are allowed"
    ),
  motherImage: z
    .instanceof(File)
    .refine(
      (file) => file.size <= 5 * 1024 * 1024,
      "File size must be less than 5MB"
    )
    .refine(
      (file) => ["image/jpeg", "image/png", "image/webp"].includes(file.type),
      "Only JPEG, PNG, or WebP images are allowed"
    ),
  gender: z.enum(["boy", "girl"], {
    required_error: "Please select a gender",
  }),
});

type FormData = z.infer<typeof formSchema>;

export default function AIBabyGeneratorPage() {
  const form = useForm<FormData>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      fatherImage: undefined,
      motherImage: undefined,
      gender: undefined,
    },
  });

  const onSubmit = (data: FormData) => {
    console.log("Form submitted:", {
      fatherImage: data.fatherImage.name,
      motherImage: data.motherImage.name,
      gender: data.gender,
    });
  };

  return (
    <div className="pt-20 bg-[url('/baby-generator/generator_bg.webp')] w-full bg-repeat h-[calc(100dvh-5rem)]">
      <div className="container max-w-2xl mx-auto text-center p-1 lg:p-4 2xl:p-12">
        <p className="text-amber-400 font-extrabold">AI Magic</p>
        <h1 className="text-6xl font-bold text-transparent bg-clip-text bg-gradient-to-r from-cyan-500 to-amber-500">
          AI Baby Generator
        </h1>
      </div>
      <div className="text-center mb-4">
        <span className="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-blue-50 text-blue-700 border border-blue-200">
          <img src="/baby-generator/icon.svg" className="w-4 h-4" />
          Free Generations Remaining: 1
        </span>
      </div>
      <div className="max-w-6xl mx-auto px-2">
        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-8">
            <div className="flex w-full">
              <FormField
                control={form.control}
                name="fatherImage"
                render={({ field }) => (
                  <FormItem className="w-1/2 mr-2 lg:mr-14">
                    <FormLabel className="font-bold flex items-center text-gray-500 text-lg">
                      Father <span className="text-red-600">*</span>
                    </FormLabel>
                    <FormControl>
                      <label
                        htmlFor="up_father"
                        className="cursor-pointer rounded-2xl mt-2 block h-36 border w-full transition-all hover:border-blue-400 hover:shadow-md"
                        style={{ border: "2px dashed rgb(177, 227, 250)" }}
                      >
                        <div className="flex flex-col bg-opacity-50 bg-white rounded-2xl items-center justify-center h-full">
                          <Image
                            src="/baby-generator/FatherIcon.svg"
                            alt="Father Icon"
                            width={48}
                            height={48}
                            className="mb-2"
                          />
                          <span className="text-gray-700 bg-white border border-gray-300 hover:bg-gray-100 focus:ring-4 focus:ring-blue-100 font-medium rounded-full text-sm px-5 py-2.5">
                            Upload Image
                          </span>
                          <Input
                            id="up_father"
                            type="file"
                            accept="image/*"
                            className="hidden"
                            onChange={(e) => {
                              const file = e.target.files?.[0];
                              if (file) field.onChange(file);
                            }}
                          />
                        </div>
                      </label>
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
              <FormField
                control={form.control}
                name="motherImage"
                render={({ field }) => (
                  <FormItem className="w-1/2">
                    <FormLabel className="font-bold flex items-center text-gray-500 text-lg">
                      Mother <span className="text-red-600">*</span>
                    </FormLabel>
                    <FormControl>
                      <label
                        htmlFor="up_mother"
                        className="cursor-pointer rounded-2xl mt-2 block h-36 border w-full transition-all hover:border-pink-400 hover:shadow-md"
                        style={{ border: "2px dashed rgb(254, 196, 176)" }}
                      >
                        <div className="flex flex-col bg-opacity-50 bg-white rounded-2xl items-center justify-center h-full">
                          <Image
                            src="/baby-generator/MotherIcon.svg"
                            alt="Mother Icon"
                            width={48}
                            height={48}
                            className="mb-2"
                          />
                          <span className="text-gray-700 bg-white border border-gray-300 hover:bg-gray-100 focus:ring-4 focus:ring-pink-100 font-medium rounded-full text-sm px-5 py-2.5">
                            Upload Image
                          </span>
                          <Input
                            id="up_mother"
                            type="file"
                            accept="image/*"
                            className="hidden"
                            onChange={(e) => {
                              const file = e.target.files?.[0];
                              if (file) field.onChange(file);
                            }}
                          />
                        </div>
                      </label>
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>
            <div className="mt-10 flex flex-col items-center justify-center h-full mx-auto">
              <FormField
                control={form.control}
                name="gender"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel className="mr-2 text-gray-700">
                      Choose Your Future Child&apos;s Gender:
                    </FormLabel>
                    <Select onValueChange={field.onChange} value={field.value}>
                      <FormControl>
                        <SelectTrigger className="w-full my-2 px-4 py-2 border rounded-full focus:outline-none focus:ring-2 focus:ring-yellow-300 h-10">
                          <SelectValue placeholder="Gender" />
                        </SelectTrigger>
                      </FormControl>
                      <SelectContent>
                        <SelectItem value="girl">Girl</SelectItem>
                        <SelectItem value="boy">Boy</SelectItem>
                      </SelectContent>
                    </Select>
                    <FormMessage />
                  </FormItem>
                )}
              />
              <Button
                type="submit"
                disabled={!form.formState.isValid}
                className="mt-4 text-black bg-gradient-to-r from-yellow-300 to-amber-400 hover:from-yellow-400 hover:to-amber-500 focus:ring-4 focus:ring-yellow-300 font-medium rounded-full text-sm px-12 py-2.5 shadow-md hover:shadow-lg transition-all transform hover:scale-105 disabled:opacity-70 disabled:cursor-not-allowed"
              >
                Generate
              </Button>
              <p className="text-sm text-gray-600 xl:w-[600px] mt-4">
                Tip: The generated image is solely an AI prediction based on
                visual features and does not reflect actual genetic inheritance
                or future appearance.
              </p>
            </div>
          </form>
        </Form>
      </div>
    </div>
  );
}
